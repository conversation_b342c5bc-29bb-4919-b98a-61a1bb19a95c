PODS:
  - camera_avfoundation (0.0.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - flutter_contacts (0.0.1):
    - Flutter
  - flutter_native_splash (2.4.3):
    - Flutter
  - flutter_pdfview (1.0.2):
    - Flutter
  - geolocator_apple (1.2.0):
    - Flutter
    - FlutterMacOS
  - image_picker_ios (0.0.1):
    - Flutter
  - integration_test (0.0.1):
    - Flutter
  - mapbox_maps_flutter (2.10.0):
    - Flutter
    - MapboxMaps (= 11.14.0)
    - Turf (= 4.0.0)
  - MapboxCommon (24.14.0):
    - Turf (= 4.0.0)
  - MapboxCoreMaps (11.14.0):
    - MapboxCommon (= 24.14.0)
  - MapboxMaps (11.14.0):
    - MapboxCommon (= 24.14.0)
    - MapboxCoreMaps (= 11.14.0)
    - Turf (= 4.0.0)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - photo_manager (3.7.1):
    - Flutter
    - FlutterMacOS
  - Sentry/HybridSDK (8.46.0)
  - sentry_flutter (8.14.2):
    - Flutter
    - FlutterMacOS
    - Sentry/HybridSDK (= 8.46.0)
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - Turf (4.0.0)
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - Flutter (from `Flutter`)
  - flutter_contacts (from `.symlinks/plugins/flutter_contacts/ios`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - flutter_pdfview (from `.symlinks/plugins/flutter_pdfview/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/darwin`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - integration_test (from `.symlinks/plugins/integration_test/ios`)
  - mapbox_maps_flutter (from `.symlinks/plugins/mapbox_maps_flutter/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - photo_manager (from `.symlinks/plugins/photo_manager/ios`)
  - sentry_flutter (from `.symlinks/plugins/sentry_flutter/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)

SPEC REPOS:
  trunk:
    - MapboxCommon
    - MapboxCoreMaps
    - MapboxMaps
    - Sentry
    - Turf

EXTERNAL SOURCES:
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  Flutter:
    :path: Flutter
  flutter_contacts:
    :path: ".symlinks/plugins/flutter_contacts/ios"
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  flutter_pdfview:
    :path: ".symlinks/plugins/flutter_pdfview/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/darwin"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  integration_test:
    :path: ".symlinks/plugins/integration_test/ios"
  mapbox_maps_flutter:
    :path: ".symlinks/plugins/mapbox_maps_flutter/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  photo_manager:
    :path: ".symlinks/plugins/photo_manager/ios"
  sentry_flutter:
    :path: ".symlinks/plugins/sentry_flutter/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"

SPEC CHECKSUMS:
  camera_avfoundation: be3be85408cd4126f250386828e9b1dfa40ab436
  connectivity_plus: cb623214f4e1f6ef8fe7403d580fdad517d2f7dd
  Flutter: cabc95a1d2626b1b06e7179b784ebcf0c0cde467
  flutter_contacts: 5383945387e7ca37cf963d4be57c21f2fc15ca9f
  flutter_native_splash: c32d145d68aeda5502d5f543ee38c192065986cf
  flutter_pdfview: 32bf27bda6fd85b9dd2c09628a824df5081246cf
  geolocator_apple: ab36aa0e8b7d7a2d7639b3b4e48308394e8cef5e
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  integration_test: 4a889634ef21a45d28d50d622cf412dc6d9f586e
  mapbox_maps_flutter: 63957d7c8978bba514fb113906e0d87767eadb5f
  MapboxCommon: 7aa10394fbcfc1d5a737d2a157c35388666e77e5
  MapboxCoreMaps: fddb4a008e3200c963a68074afccd8f6d816544c
  MapboxMaps: 308bb0be5e7a59c9f68a3b06f48998ccb4477583
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  photo_manager: 1d80ae07a89a67dfbcae95953a1e5a24af7c3e62
  Sentry: da60d980b197a46db0b35ea12cb8f39af48d8854
  sentry_flutter: 27892878729f42701297c628eb90e7c6529f3684
  share_plus: 50da8cb520a8f0f65671c6c6a99b3617ed10a58a
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  Turf: c9eb11a65d96af58cac523460fd40fec5061b081
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  video_player_avfoundation: 2cef49524dd1f16c5300b9cd6efd9611ce03639b

PODFILE CHECKSUM: 7bb08e8bd89bec0588bdb44b8f8bbb6ec0b86ee0

COCOAPODS: 1.16.2
