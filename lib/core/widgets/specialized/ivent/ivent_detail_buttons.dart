import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_blurred_button.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_blurred_icon_button.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_rounded_button.dart';
import 'package:ivent_app/core/widgets/foundation/graphics/ia_svg_icon.dart';

class IventDetailButtons {
  IventDetailButtons._();

  static IaRoundedButton iventDetailJoin({
    Key? key,
    EdgeInsetsGeometry? margin,
    required VoidCallback onTap,
  }) {
    return IaRoundedButton(
      key: key,
      margin: margin,
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding24),
      height: AppDimensions.buttonHeightIventDetail,
      roundness: AppDimensions.buttonRadiusS,
      onTap: onTap,
      color: AppColors.primary,
      text: 'Etkinliğe Katıl',
      textStyle: AppTextStyles.size14BoldWhite,
      expand: false,
    );
  }

  static Expanded _iventDetailCommunication({
    Key? key,
    EdgeInsetsGeometry? margin,
    required VoidCallback onTap,
    required Color color,
    required String text,
    required Color textColor,
    required String iconPath,
  }) {
    return Expanded(
      child: IaRoundedButton(
        key: key,
        margin: margin,
        height: AppDimensions.buttonHeightIventDetail,
        roundness: AppDimensions.radiusXXL,
        onTap: onTap,
        color: color,
        text: text,
        textStyle: AppTextStyles.buttonTextMedium.copyWith(color: textColor),
        leading: IaSvgIcon(iconPath: iconPath, iconColor: textColor),
      ),
    );
  }

  static Expanded iventDetailCommunicationInstagram({
    Key? key,
    EdgeInsetsGeometry? margin,
    required VoidCallback onTap,
  }) {
    return _iventDetailCommunication(
      key: key,
      margin: margin,
      onTap: onTap,
      color: AppColors.black,
      text: 'DM',
      textColor: AppColors.white,
      iconPath: AppAssets.instagram,
    );
  }

  static Expanded iventDetailCommunicationCall({
    Key? key,
    EdgeInsetsGeometry? margin,
    required VoidCallback onTap,
  }) {
    return _iventDetailCommunication(
      key: key,
      margin: margin,
      onTap: onTap,
      color: AppColors.primary,
      text: 'Arama',
      textColor: AppColors.white,
      iconPath: AppAssets.phone,
    );
  }

  static Expanded iventDetailCommunicationLink({
    Key? key,
    EdgeInsetsGeometry? margin,
    required VoidCallback onTap,
  }) {
    return _iventDetailCommunication(
      key: key,
      margin: margin,
      onTap: onTap,
      color: AppColors.primary,
      text: 'Link',
      textColor: AppColors.white,
      iconPath: AppAssets.link,
    );
  }

  static Expanded iventDetailCommunicationGoogleForms({
    Key? key,
    EdgeInsetsGeometry? margin,
    required VoidCallback onTap,
  }) {
    return _iventDetailCommunication(
      key: key,
      margin: margin,
      onTap: onTap,
      color: AppColors.purpleLight,
      text: 'Form',
      textColor: AppColors.purple,
      iconPath: AppAssets.googleForms,
    );
  }

  static Expanded iventDetailCommunicationWhatsappGroup({
    Key? key,
    EdgeInsetsGeometry? margin,
    required VoidCallback onTap,
  }) {
    return _iventDetailCommunication(
      key: key,
      margin: margin,
      onTap: onTap,
      color: AppColors.purpleLight,
      text: 'Grup',
      textColor: AppColors.purple,
      iconPath: AppAssets.whatsapp,
    );
  }

  static Expanded iventDetailCommunicationWhatsappChat({
    Key? key,
    EdgeInsetsGeometry? margin,
    required VoidCallback onTap,
  }) {
    return _iventDetailCommunication(
      key: key,
      margin: margin,
      onTap: onTap,
      color: AppColors.whatsappGreen,
      text: 'Mesaj',
      textColor: AppColors.white,
      iconPath: AppAssets.whatsapp,
    );
  }

  static IaRoundedButton iventDetailTag({
    Key? key,
    EdgeInsetsGeometry? margin,
    VoidCallback? onTap,
    required String text,
    String? iconPath,
  }) {
    return IaRoundedButton(
      key: key,
      margin: margin,
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding12),
      height: AppDimensions.buttonHeightIventTag,
      roundness: AppDimensions.buttonRadiusS,
      onTap: onTap,
      color: AppColors.lightGrey,
      text: text,
      textStyle: AppTextStyles.size16Medium,
      leading: iconPath != null ? IaSvgIcon(iconPath: iconPath) : null,
      expand: false,
    );
  }

  static IaBlurredButton favoriteIventThumbnailDetailed({
    Key? key,
    EdgeInsetsGeometry? margin,
    VoidCallback? onTap,
    required int favoriteCount,
  }) {
    return IaBlurredButton(
      key: key,
      margin: margin,
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding12),
      height: AppDimensions.buttonHeightFavoriteIventThumbnailDetailed,
      roundness: AppDimensions.radiusS,
      onTap: onTap,
      color: AppColors.black.withValues(alpha: 0.2),
      text: '${favoriteCount.toString()} Favori',
      textStyle: AppTextStyles.buttonTextMedium,
      leading: const IaSvgIcon(iconPath: AppAssets.star, iconColor: AppColors.starYellow),
    );
  }

  static IaBlurredIconButton shareIventThumbnailL({
    Key? key,
    VoidCallback? onTap,
  }) {
    return IaBlurredIconButton(
      key: key,
      color: AppColors.black.withValues(alpha: 0.2),
      onTap: onTap,
      roundness: AppDimensions.radiusS,
      width: AppDimensions.actionButtonSizeL,
      height: AppDimensions.actionButtonSizeL,
      iconPath: AppAssets.shareAndroid,
      iconSize: AppDimensions.actionButtonSizeL * 1 / 2,
      iconColor: AppColors.white,
    );
  }

  static Row actionButtonL({
    Key? key,
    VoidCallback? onFavorite,
    VoidCallback? onShare,
    required bool isFavorited,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        const SizedBox(width: AppDimensions.padding8),
        IventDetailButtons.shareIventThumbnailL(onTap: onShare),
      ],
    );
  }

  static IaBlurredButton actionButtonLCreator({
    Key? key,
    VoidCallback? onTap,
    required int favoriteCount,
  }) {
    return IventDetailButtons.favoriteIventThumbnailDetailed(
      onTap: onTap,
      favoriteCount: favoriteCount,
    );
  }
}
