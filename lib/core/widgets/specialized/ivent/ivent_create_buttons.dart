import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/foundation/graphics/ia_svg_icon.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_circular_button.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_icon_button.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_rounded_button.dart';

class IventCreateButtons {
  IventCreateButtons._();
 
  static IaRoundedButton iventCreatePublishWithoutRegister({
    Key? key,
    EdgeInsetsGeometry? margin,
    required VoidCallback onTap,
    required bool isEnabled,
  }) {
    return IaRoundedButton(
      key: key,
      margin: margin,
      width: double.maxFinite,
      height: AppDimensions.buttonHeightIventCreate,
      roundness: AppDimensions.buttonRadiusS,
      onTap: onTap,
      color: AppColors.lightGrey,
      text: 'Kayıt Almadan Yayınla',
      textStyle: AppTextStyles.size16Bold.copyWith(color: isEnabled ? AppColors.secondary : AppColors.mediumGrey),
    );
  }

  static IaRoundedButton iventCreatePublish({
    Key? key,
    EdgeInsetsGeometry? margin,
    required VoidCallback onTap,
    required bool isEnabled,
  }) {
    return IaRoundedButton(
      key: key,
      margin: margin,
      width: double.maxFinite,
      height: AppDimensions.buttonHeightIventCreate,
      roundness: AppDimensions.buttonRadiusS,
      onTap: onTap,
      color: isEnabled ? AppColors.secondary : AppColors.darkGrey,
      text: 'Yayınla!',
      textStyle: AppTextStyles.size16Bold.copyWith(color: AppColors.white),
    );
  }

  static IaRoundedButton iventCreateTag({
    Key? key,
    EdgeInsetsGeometry? margin,
    required VoidCallback onTap,
    required bool isSelected,
    required String text,
  }) {
    return IaRoundedButton(
      key: key,
      margin: margin,
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding12),
      height: AppDimensions.buttonHeightSelectedIventTag,
      roundness: AppDimensions.buttonRadiusS,
      onTap: isSelected ? null : onTap,
      color: isSelected ? AppColors.primary : AppColors.white,
      text: text,
      textStyle: isSelected
          ? AppTextStyles.buttonTextMedium
          : AppTextStyles.buttonTextMedium.copyWith(color: AppColors.darkGrey),
      trailing: isSelected
          ? IaIconButton(
              iconPath: AppAssets.closeSM,
              iconColor: AppColors.white,
              iconSize: AppDimensions.defaultIconButtonSize * 0.6,
              onPressed: onTap,
            )
          : const IaSvgIcon(
              iconPath: AppAssets.addPlus,
              iconColor: AppColors.darkGrey,
            ),
      expand: false,
    );
  }

  static IaRoundedButton whatsappJoin({
    Key? key,
    EdgeInsetsGeometry? margin,
    required VoidCallback onTap,
    required bool isActive,
  }) {
    return IaRoundedButton(
      key: key,
      margin: margin,
      height: AppDimensions.buttonHeightWhatsappJoin,
      roundness: AppDimensions.radiusXS,
      onTap: onTap,
      color: isActive ? AppColors.primary : AppColors.grey400,
      text: 'Katılım İsteği',
      textStyle: AppTextStyles.buttonTextMedium.copyWith(color: isActive ? AppColors.white : AppColors.grey600),
      trailing: isActive
          ? const IaSvgIcon(iconPath: AppAssets.lock, iconColor: AppColors.white)
          : const IaSvgIcon(iconPath: AppAssets.lockOpen, iconColor: AppColors.grey600),
    );
  }

  static IaRoundedButton privacyIvent({
    Key? key,
    EdgeInsetsGeometry? margin,
    required VoidCallback onTap,
    required String text,
    required String iconPath,
  }) {
    return IaRoundedButton(
      key: key,
      margin: margin,
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding4),
      height: AppDimensions.buttonHeightPrivacyIvent,
      roundness: AppDimensions.radiusS,
      onTap: onTap,
      color: AppColors.lightGrey,
      text: text,
      textStyle: AppTextStyles.buttonTextMedium.copyWith(color: AppColors.darkGrey),
      leading: IaSvgIcon(iconPath: iconPath, iconColor: AppColors.darkGrey),
      trailing: const IaSvgIcon(iconPath: AppAssets.caretDownSM, iconColor: AppColors.mediumGrey),
      expand: false,
    );
  }

  static IaCircularButton mapBackButton({
    Key? key,
    VoidCallback? onTap,
  }) {
    return IaCircularButton(
      key: key,
      onPressed: Get.back,
      buttonSize: AppDimensions.defaultIconButtonSize,
      backgroundColor: AppColors.black.withValues(alpha: 0.5),
      iconPath: AppAssets.chevronLeft,
      iconSize: AppDimensions.defaultIconButtonSize,
    );
  }

  static IaCircularButton findUserLocation({
    Key? key,
    VoidCallback? onTap,
  }) {
    return IaCircularButton(
      key: key,
      onPressed: onTap,
      buttonSize: AppDimensions.defaultIconButtonSize,
      backgroundColor: AppColors.black.withValues(alpha: 0.5),
      iconPath: AppAssets.navigation,
      iconSize: AppDimensions.defaultIconButtonSize,
      iconColor: AppColors.white,
    );
  }

  static IaIconButton closeButtonDark({
    Key? key,
    VoidCallback? onTap,
  }) {
    return IaIconButton(
      key: key,
      onPressed: onTap,
      buttonSize: AppDimensions.defaultIconButtonSize * 0.8,
      iconPath: AppAssets.closeSM,
      iconSize: AppDimensions.defaultIconButtonSize * 0.8,
      iconColor: AppColors.grey800,
    );
  }
}
