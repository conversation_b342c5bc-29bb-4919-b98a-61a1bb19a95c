import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_icon_button.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_text_button.dart';
import 'package:ivent_app/core/widgets/foundation/containers/ia_image_container.dart';
import 'package:ivent_app/core/widgets/foundation/containers/ia_rounded_container.dart';
import 'package:ivent_app/core/widgets/specialized/ivent/ivent_detail_buttons.dart';
import 'package:ivent_app/features/ivent_detail/ivent_detail_pages.dart';

class IaIventThumbnail extends StatelessWidget {
  final EdgeInsetsGeometry? margin;
  final String iventId;
  final String iventName;
  final String locationId;
  final String locationName;
  final List<DateTime>? date;
  final String? thumbnailUrl;
  final bool isFavorited;
  final VoidCallback? onFavorite;
  final VoidCallback? onShare;
  final double buttonSize;
  final double roundness;
  final double padding;
  final TextStyle iventNameStyle;
  final TextStyle locationNameStyle;
  final TextStyle dateStyle;
  final String trailingIconPath;
  final VoidCallback? trailingOnTap;
  final Widget? actionButton;

  const IaIventThumbnail._({
    super.key,
    this.margin,
    required this.iventId,
    required this.iventName,
    required this.locationId,
    required this.locationName,
    this.date,
    required this.thumbnailUrl,
    required this.isFavorited,
    this.onFavorite,
    this.onShare,
    required this.buttonSize,
    required this.roundness,
    required this.padding,
    required this.iventNameStyle,
    required this.locationNameStyle,
    required this.dateStyle,
    required this.trailingIconPath,
    this.trailingOnTap,
    this.actionButton,
  });

  static const EdgeInsetsGeometry _defaultMargin = const EdgeInsets.all(0);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: margin ?? _defaultMargin,
      child: Stack(
        children: [
          _buildThumbnailImage(),
          _buildThumbnailOverlay(),
        ],
      ),
    );
  }

  /// Builds the thumbnail image container
  Widget _buildThumbnailImage() {
    return IaImageContainer.withImageUrl(imageUrl: thumbnailUrl, roundness: roundness);
  }

  /// Builds the gradient overlay with ivent details and action buttons
  Widget _buildThumbnailOverlay() {
    return Positioned.fill(
      child: IaRoundedContainer(
        onTap: () => Get.toNamed(IventDetailPages.iventDetail, arguments: iventId),
        padding: EdgeInsets.all(padding),
        roundness: roundness,
        gradient: date != null ? AppColors.gradientBlackL : AppColors.gradientBlackS,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildIventTitle(),
            if (date != null) const SizedBox(height: AppDimensions.padding8),
            _buildLocationName(),
            if (date != null) const SizedBox(height: AppDimensions.padding8),
            if (date != null) _buildDate(),
            if (date != null) const SizedBox(height: AppDimensions.padding4),
            if (date != null) _buildShowLocationButton(),
            const Spacer(),
            if (actionButton != null) _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  /// Builds the ivent title row with arrow icon
  Widget _buildIventTitle() {
    return Row(
      children: [
        Expanded(
          child: Text(
            iventName,
            style: iventNameStyle,
            maxLines: 1,
            softWrap: false,
          ),
        ),
        const SizedBox(width: AppDimensions.padding12),
        IaIconButton(iconPath: trailingIconPath, onPressed: trailingOnTap),
      ],
    );
  }

  /// Builds the location name text
  Widget _buildLocationName() {
    return Text(
      locationName,
      style: locationNameStyle,
      maxLines: 1,
      softWrap: false,
    );
  }

  /// Builds the date text
  Widget _buildDate() {
    final formattedDate = DateFormat('d MMMM yyyy, HH:mm').format(date![0]); // TODO: FIX
    return Text(
      formattedDate,
      style: dateStyle,
      maxLines: 1,
      softWrap: false,
    );
  }

  /// Builds the map button
  Widget _buildShowLocationButton() {
    return IaTextButton(
      text: 'Haritada görmek için tıkla...',
      textStyle: AppTextStyles.size12RegularTextTertiary,
      maxLines: 1,
      softWrap: false,
    );
  }

  /// Builds the action buttons (favorite and share)
  Widget _buildActionButtons() => Align(alignment: Alignment.bottomRight, child: actionButton);

  static IaIventThumbnail big({
    Key? key,
    EdgeInsetsGeometry? margin,
    required String iventId,
    required String iventName,
    required String locationId,
    required String locationName,
    required List<DateTime> date,
    required String? thumbnailUrl,
    required bool isFavorited,
    int? favoriteCount,
    VoidCallback? onTap,
    VoidCallback? onFavorite,
    VoidCallback? onShare,
  }) {
    return IaIventThumbnail._(
      key: key,
      margin: margin,
      iventId: iventId,
      iventName: iventName,
      locationId: locationId,
      locationName: locationName,
      date: date,
      thumbnailUrl: thumbnailUrl,
      isFavorited: isFavorited,
      onFavorite: onFavorite,
      onShare: onShare,
      buttonSize: AppDimensions.actionButtonSizeL,
      roundness: AppDimensions.radiusS,
      padding: AppDimensions.padding16,
      iventNameStyle: AppTextStyles.size32BoldWhite,
      locationNameStyle: AppTextStyles.size24BoldWhite,
      dateStyle: AppTextStyles.size24BoldWhite,
      trailingIconPath: AppAssets.moreHorizontal,
      trailingOnTap: () {},
      actionButton: favoriteCount == null
          ? IventDetailButtons.actionButtonL(
              onFavorite: onFavorite,
              onShare: onShare,
              isFavorited: isFavorited,
            )
          : IventDetailButtons.actionButtonLCreator(
              onTap: onFavorite,
              favoriteCount: favoriteCount,
            ),
    );
  }

  static IaIventThumbnail small({
    Key? key,
    required String iventId,
    required String iventName,
    required String locationId,
    required String locationName,
    required String? thumbnailUrl,
    required bool isFavorited,
    VoidCallback? onFavorite,
    VoidCallback? onShare,
  }) {
    return IaIventThumbnail._(
      key: key,
      iventId: iventId,
      iventName: iventName,
      locationId: locationId,
      locationName: locationName,
      thumbnailUrl: thumbnailUrl,
      isFavorited: isFavorited,
      onFavorite: onFavorite,
      onShare: onShare,
      buttonSize: AppDimensions.actionButtonSizeS,
      roundness: AppDimensions.radiusM,
      padding: AppDimensions.padding8,
      iventNameStyle: AppTextStyles.size14BoldWhite,
      locationNameStyle: AppTextStyles.size10RegularWhite,
      dateStyle: AppTextStyles.size14Bold,
      trailingIconPath: AppAssets.caretRightSM,
    );
  }
}
