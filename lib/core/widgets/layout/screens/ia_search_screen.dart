import 'package:flutter/widgets.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/foundation/inputs/ia_search_bar.dart';

class IaSearchScreen extends StatelessWidget {
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? searchBarMargin;
  final TextEditingController textController;
  final String? searchBarLabelText;
  final Widget body;

  const IaSearchScreen({
    super.key,
    this.margin,
    this.searchBarMargin,
    required this.textController,
    this.searchBarLabelText,
    required this.body,
  });

  static const EdgeInsets _defaultMargin = const EdgeInsets.all(0);
  static const EdgeInsets _defaultSearchBarMargin = const EdgeInsets.symmetric(vertical: AppDimensions.padding20);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: margin ?? _defaultMargin,
      child: Column(
        children: [
          IaSearchBar(
            margin: searchBarMargin ?? _defaultSearchBarMargin,
            textEditingController: textController,
            searchBarLabelText: searchBarLabelText,
          ),
          Expanded(child: body),
        ],
      ),
    );
  }
}
