import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/widgets/layout/panels/ia_bottom_panel.dart';
import 'package:sliding_up_panel/sliding_up_panel.dart';

class IaSlidingPanel extends StatelessWidget {
  final PanelState defaultPanelState;
  final bool isDraggable;
  final Widget body;
  final double? maxHeight;
  final double minHeight;
  final IaBottomPanel panel;
  final VoidCallback? onPanelOpened;
  final VoidCallback? onPanelClosed;
  final PanelController? panelController;

  const IaSlidingPanel({
    super.key,
    this.defaultPanelState = PanelState.OPEN,
    this.isDraggable = true,
    required this.body,
    this.maxHeight,
    required this.minHeight,
    required this.panel,
    this.onPanelOpened,
    this.onPanelClosed,
    this.panelController,
  });

  @override
  Widget build(BuildContext context) {
    return SlidingUpPanel(
      controller: panelController,
      onPanelOpened: onPanelOpened,
      onPanelClosed: onPanelClosed,
      defaultPanelState: defaultPanelState,
      isDraggable: isDraggable,
      borderRadius: const BorderRadius.only(
        topLeft: Radius.circular(30),
        topRight: Radius.circular(30),
      ),
      maxHeight: maxHeight ?? Get.height,
      minHeight: minHeight,
      panel: panel,
      body: body,
    );
  }
}
