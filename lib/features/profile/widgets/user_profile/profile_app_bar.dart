import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_icon_button.dart';
import 'package:ivent_app/core/widgets/ia_app_bar.dart';
import 'package:ivent_app/core/widgets/ia_builder.dart';
import 'package:ivent_app/features/profile/controllers/profile_user_info_controller.dart';
import 'package:ivent_app/routes/settings.dart';

class ProfileAppBar extends GetView<ProfileUserInfoController> {
  final String userId;

  const ProfileAppBar({super.key, required this.userId});

  @override
  String? get tag => userId;

  @override
  Widget build(BuildContext context) {
    return Obx(() => IaBuilder(
        isLoading: controller.isLoading('loadUserInfo'),
        isEmpty: controller.userPageInfo == null,
        builder: (context) => IaAppBar.centered(
              title: controller.userPageInfo!.fullname,
              subtitle: controller.userPageInfo!.username,
              leading: const _Leading(),
              trailing: const _Trailing(),
            )));
  }
}

class _Leading extends StatelessWidget {
  const _Leading();

  @override
  Widget build(BuildContext context) {
    return IaIconButton(
      onPressed: Scaffold.of(context).openDrawer,
      iconPath: AppAssets.hamburgerMD,
      iconSize: AppDimensions.defaultIconButtonSize,
      iconColor: AppColors.darkGrey,
    );
  }
}

class _Trailing extends StatelessWidget {
  const _Trailing();

  @override
  Widget build(BuildContext context) {
    return IaIconButton(
      onPressed: () => Get.toNamed(SettingsPages.settings),
      iconPath: AppAssets.settings,
      iconSize: AppDimensions.defaultIconButtonSize,
      iconColor: AppColors.darkGrey,
    );
  }
}
