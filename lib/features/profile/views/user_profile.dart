import 'package:flutter/material.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/profile/widgets/user_profile/content_tabs.dart';
import 'package:ivent_app/features/profile/widgets/user_profile/profile_app_bar.dart';
import 'package:ivent_app/features/profile/widgets/user_profile/user_info_widget.dart';

class UserProfile extends StatelessWidget {
  final String userId;

  const UserProfile({Key? key, required this.userId}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return IaScaffold.basic(
      appBar: ProfileAppBar(userId: userId),
      body: DefaultTabController(
        length: 2,
        child: NestedScrollView(
          headerSliverBuilder: (context, value) => [
            SliverToBoxAdapter(child: UserInfoWidget(userId: userId)),
          ],
          body: ContentTabs(userId: userId),
        ),
      ),
    );
  }
}
