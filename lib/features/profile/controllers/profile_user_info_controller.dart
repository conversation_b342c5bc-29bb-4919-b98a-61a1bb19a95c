import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/profile/controllers/profile_shared_state.dart';

class ProfileUserInfoController extends BaseController<ProfileSharedState> {
  final _userPageInfo = Rxn<GetUserByUserIdReturn>();
  final _isFollowing = false.obs;
  final _relationshipStatus = Rxn<UserRelationshipStatusEnum>();

  GetUserByUserIdReturn? get userPageInfo => _userPageInfo.value;
  bool get isFollowing => _isFollowing.value;
  UserRelationshipStatusEnum? get relationshipStatus => _relationshipStatus.value;

  ProfileUserInfoController(AuthService authService, ProfileSharedState state) : super(authService, state);

  @override
  Future<void> onInitAsync() async {
    super.onInitAsync();
    await _loadUserInfo();
  }

  Future<void> _loadUserInfo() async {
    await runSafe(tag: 'loadUserInfo', () async {
      _userPageInfo.value = await usersApi.getByUserId(state.userId);
      _isFollowing.value = _userPageInfo.value!.isFollowing;
      _relationshipStatus.value = _userPageInfo.value!.relationshipStatus;
      state.userRole = _userPageInfo.value!.userRole;
    });
  }

  Future<void> toggleFollowing() async {
    await runSafe(tag: 'toggleFollowing', () async {
      if (isFollowing) {
        _isFollowing.value = false;
        await usersApi.unfollowByUserId(state.userId);
      } else {
        _isFollowing.value = true;
        await usersApi.followByUserId(state.userId);
      }
    });
  }

  Future<void> toggleFriendship() async {
    await runSafe(tag: 'toggleFriendship', () async {
      if (relationshipStatus == UserRelationshipStatusEnum.accepted) {
        _relationshipStatus.value = null;
        await userRelationshipsApi.removeFriendByUserId(state.userId);
      } else {
        _relationshipStatus.value = UserRelationshipStatusEnum.accepted;
        await userRelationshipsApi.inviteFriendByUserId(state.userId);
      }
    });
  }
}
