import 'dart:async';

import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/core/widgets/ia_search_results_builder.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_state_manager.dart';
import 'package:ivent_app/features/mapbox/controllers/mapbox_controller.dart';
import 'package:ivent_app/features/mapbox/models/ia_location_item.dart';
import 'package:ivent_app/features/mapbox/models/marker_feature.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';
import 'package:sliding_up_panel/sliding_up_panel.dart';

class IventCreateMapController extends BaseControllerWithSearch<IventCreateSharedState> {
  late final MapboxController mapboxController;

  final _markers = <PointAnnotationOptions>[].obs;
  final _panelController = PanelController();
  final _searchSuggestions = <SearchBoxSuggestFeature>[].obs;

  IventCreateMapController(AuthService authService, IventCreateSharedState state) : super(authService, state) {
    mapboxController = MapboxController(authService: authService);
  }

  List<PointAnnotationOptions> get markers => _markers;
  PanelController get panelController => _panelController;
  List<SearchBoxSuggestFeature> get searchSuggestions => _searchSuggestions;

  @override
  bool get isResultsEmpty => searchSuggestions.isEmpty;

  @override
  InitialSearchBehavior get initialSearchBehavior => InitialSearchBehavior.mustSearch;

  set markers(List<PointAnnotationOptions> value) => _markers.assignAll(value);

  @override
  Future<void> onSearch([String? query]) async {
    if (query == null || query.isEmpty) {
      searchSuggestions.clear();
      return;
    }

    await runSafe(
      () async {
        final result = await mapboxApi.searchBoxSuggest(
          query,
          sessionUser.sessionId,
          limit: 10,
          proximity: mapboxController.userLocation != null
              ? '${mapboxController.userLocationCoordinates!.lng},${mapboxController.userLocationCoordinates!.lat}'
              : null,
          types: 'country,region,postcode,district,place,city,locality,neighborhood,street,address,poi,category',
        );

        if (result != null) {
          searchSuggestions.assignAll(result.suggestions);
        }
      },
      tag: 'searchPlaces',
    );
  }

  Future<void> selectPlace(SearchBoxSuggestFeature suggestion) async {
    try {
      final result = await mapboxApi.searchBoxRetrieve(
        suggestion.mapboxId,
        sessionUser.sessionId,
      );

      if (result != null && result.features.isNotEmpty) {
        await _updateSelectedPlace(result.features[0], suggestion.name);
      }
    } catch (e) {
      handleError(e);
    }
  }

  void clearSearch() {
    textController.clear();
    searchSuggestions.clear();
    state.selectedPlace.value = null;
    markers = [];
    _removeSelectedPlaceMarker();
  }

  Future<void> _updateSelectedPlace(SearchBoxFeature feature, String placeName) async {
    state.selectedPlace.value = IaLocationItem.fromProperties(feature.properties);
    panelController.close();
    textController.text = placeName;
    searchSuggestions.clear();

    _removeSelectedPlaceMarker();
    _addSelectedPlaceMarker();

    await _flyToSelectedPlace();
  }

  void _removeSelectedPlaceMarker() {
    mapboxController.markerController.removeMarkers(['selected-place']);
  }

  void _addSelectedPlaceMarker() {
    if (state.selectedPlace.value == null) return;

    mapboxController.markerController.addMarkers([
      MarkerFeature(
        id: 'selected-place',
        properties: MarkerFeatureProperties(isSelected: false),
        geometry: MarkerFeatureGeometry(
          coordinates: [
            state.selectedPlace.value!.longitude,
            state.selectedPlace.value!.latitude,
          ],
        ),
      )
    ]);
  }

  Future<void> _flyToSelectedPlace() async {
    if (state.selectedPlace.value == null) return;

    await mapboxController.mapboxMap.flyTo(
      CameraOptions(
        center: Point(
          coordinates: Position(
            state.selectedPlace.value!.longitude,
            state.selectedPlace.value!.latitude,
          ),
        ),
        zoom: 14,
      ),
      MapAnimationOptions(duration: 50),
    );
  }
}
