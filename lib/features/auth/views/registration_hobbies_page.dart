import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/ia_search_results_builder.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/auth/constants/auth_dimensions.dart';
import 'package:ivent_app/features/auth/constants/strings.dart';
import 'package:ivent_app/features/auth/controllers/auth_registration_controller.dart';
import 'package:ivent_app/features/auth/widgets/hobby_category_box.dart';
import 'package:ivent_app/shared/domain/entities/hobby.dart';

class RegistrationHobbiesPage extends GetView<AuthRegistrationController> {
  const RegistrationHobbiesPage({super.key});

  @override
  Widget build(BuildContext context) {
    return IaScaffold.noSearch(
      showBackButton: false,
      title: AuthStrings.ilgiAlani,
      bodyPadding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: AppDimensions.padding20),
          Text(
            AuthStrings.ilgiAlaniText,
            style: AppTextStyles.size14MediumTextSecondary,
            maxLines: null,
          ),
          Expanded(
            child: IaSearchScreen(
              textController: controller.textController,
              body: Obx(() {
                debugPrint('[Obx] 1');
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (controller.checkedHobbyIds.isNotEmpty)
                      Container(
                        margin: const EdgeInsets.only(bottom: AuthDimensions.formElementSpacing),
                        height: AppDimensions.buttonHeightSelectedHobbyTag,
                        child: ListView.separated(
                          scrollDirection: Axis.horizontal,
                          shrinkWrap: true,
                          itemCount: controller.checkedHobbyIds.length,
                          itemBuilder: (context, index) => HobbyButtons.selectedHobbyTag(
                            onTap: () => controller.toggleHobby(controller.checkedHobbyIds[index]),
                            text: Hobby.getHobbyNameFromHobbyId(controller.checkedHobbyIds[index]),
                          ),
                          separatorBuilder: (context, index) =>
                              const SizedBox(width: AuthDimensions.relatedElementSpacing),
                        ),
                      ),
                    Expanded(
                      child: controller.isQueryEmpty
                          ? _DefaultScreen(controller: controller)
                          : _SearchScreen(controller: controller),
                    ),
                  ],
                );
              }),
            ),
          ),
        ],
      ),
      floatingActionButton: Obx(() {
        debugPrint('[Obx] 2');
        return IaFloatingActionButton(
          isEnabled: controller.areHobbiesValid,
          text: AuthStrings.devamEt,
          onPressed: controller.completeRegistration,
        );
      }),
    );
  }
}

class _DefaultScreen extends StatelessWidget {
  const _DefaultScreen({
    required this.controller,
  });

  final AuthRegistrationController controller;

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      debugPrint('[Obx] 3 - DefaultScreen');
      return ListView.separated(
        padding: const EdgeInsets.only(bottom: 100),
        itemCount: controller.hobbyCategories.length,
        itemBuilder: (context, index) {
          return HobbyCategoryBox(
            mainCategory: controller.hobbyCategories.elementAt(index),
            hobbyList: controller.hobbyLists.elementAt(index),
            selectedHobbyIds: controller.checkedHobbyIds,
            onHobbyToggle: controller.toggleHobby,
          );
        },
        separatorBuilder: (context, index) => const SizedBox(height: AuthDimensions.hobbyCategorySpacing),
      );
    });
  }
}

class _SearchScreen extends StatelessWidget {
  const _SearchScreen({
    required this.controller,
  });

  final AuthRegistrationController controller;

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      debugPrint('[Obx] 4');
      return IaSearchResultsBuilder(
        entityName: 'İlgi Alanı',
        isSearching: controller.isSearching,
        isQueryEmpty: controller.isQueryEmpty,
        isResultsEmpty: controller.isResultsEmpty,
        initialSearchBehavior: controller.initialSearchBehavior,
        builder: (context) {
          return ListView.separated(
            padding: const EdgeInsets.only(bottom: 100),
            itemCount: min(controller.searchResults.length, 5),
            itemBuilder: (context, index) {
              final hobby = controller.searchResults[index];
              return IaTextButton(
                onPressed: () {
                  controller.toggleHobby(hobby.hobbyId);
                  controller.clearSearch();
                },
                text: hobby.hobbyName,
                textStyle: AppTextStyles.size16Regular,
              );
            },
            separatorBuilder: IaListTile.separatorBuilder20,
          );
        },
      );
    });
  }
}
