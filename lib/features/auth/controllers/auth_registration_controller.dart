import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/cache/cache_manager.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/core/widgets/ia_search_results_builder.dart';
import 'package:ivent_app/features/auth/auth_pages.dart';
import 'package:ivent_app/features/auth/constants/validation_constants.dart';
import 'package:ivent_app/features/auth/controllers/auth_shared_state.dart';
import 'package:ivent_app/shared/domain/entities/hobby.dart';

class AuthRegistrationController extends BaseControllerWithSearch<AuthSharedState> {
  AuthRegistrationController(AuthService authService, AuthSharedState state) : super(authService, state);

  @override
  bool get isResultsEmpty => false;

  @override
  InitialSearchBehavior get initialSearchBehavior => InitialSearchBehavior.mustSearch;

  @override
  Future<void> onSearch([String? query]) async {
    if (query == null || query.isEmpty) {
      _searchResults.assignAll(allHobbies);
    } else {
      final toAssign = allHobbies
          .where((hobby) =>
              hobby.hobbyName.toLowerCase().contains(query.toLowerCase()) ||
              (hobby.parentHobbyName?.toLowerCase().contains(query.toLowerCase()) ?? false))
          .toList();
      _searchResults.assignAll(toAssign.sublist(0, min(5, toAssign.length)));
    }
  }

  // Name page
  final _fullnameTextController = TextEditingController();
  final _fullname = ''.obs;
  final _canContinueToHobbiesPage = false.obs;

  TextEditingController get fullnameTextController => _fullnameTextController;
  String get fullname => _fullname.value;
  bool get canContinueToHobbiesPage => _canContinueToHobbiesPage.value;
  bool get isFullnameValid =>
      fullname.trim().length >= AuthValidationConstants.fullNameMinLength &&
      fullname.trim().length <= AuthValidationConstants.fullNameMaxLength;

  void handleFullnameChanged(String value) => _fullname.value = value;
  void handleFullnameValidationChanged(bool isValid) => _canContinueToHobbiesPage.value = isValid;
  void goToHobbiesPage() => Get.toNamed(AuthPages.registrationHobbiesView);

  // Hobbies page
  final _checkedHobbyIds = <String>[].obs;
  final _searchResults = <Hobby>[].obs;
  final allHobbiesByCategories = {
    'Müzik': Hobby.hobbyListByParentHobbyName['Müzik']!,
    'Sanat & Kültür': Hobby.hobbyListByParentHobbyName['Sanat & Kültür']!,
    'Spor': Hobby.hobbyListByParentHobbyName['Spor']!,
    'Kariyer & Akademik': Hobby.hobbyListByParentHobbyName['Kariyer & Akademik']!,
    'Yeme İçme': Hobby.hobbyListByParentHobbyName['Yeme İçme']!,
    'Toplum': Hobby.hobbyListByParentHobbyName['Toplum']!,
  };
  final allHobbies = Hobby.hobbyList;

  List<String> get checkedHobbyIds => _checkedHobbyIds.toList();
  List<Hobby> get searchResults => _searchResults.toList();
  bool get areHobbiesValid => checkedHobbyIds.length >= AuthValidationConstants.minRequiredHobbies;

  List<String> get hobbyCategories => allHobbiesByCategories.keys.toList();
  List<List<Hobby>> get hobbyLists => allHobbiesByCategories.values.toList();

  void toggleHobby(String hobbyId) {
    if (checkedHobbyIds.contains(hobbyId)) {
      checkedHobbyIds.remove(hobbyId);
    } else {
      checkedHobbyIds.add(hobbyId);
    }
  }

  Future<void> _registerUser() async {
    await runSafe(() async {
      final result = await usersApi.register(
        RegisterDto(
          fullname: fullname,
          phoneNumber: state.formattedPhoneNumber,
          hobbyIds: checkedHobbyIds,
        ),
      );

      if (result == null) {
        goToSomethingWentWrongPage();
        return;
      }

      await authService.login(
        SessionUser(
          token: result.token,
          sessionId: result.userId,
          sessionRole: result.role,
          sessionUsername: result.username,
          sessionFullname: result.fullname,
          sessionAvatarUrl: result.avatarUrl,
        ),
      );
    });
  }

  Future<void> completeRegistration() async {
    await _registerUser();
    Get.toNamed(AuthPages.accessPage);
  }
}
