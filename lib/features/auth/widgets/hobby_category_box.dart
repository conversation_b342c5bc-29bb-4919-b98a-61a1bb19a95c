import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/composite/buttons/hobby_buttons.dart';
import 'package:ivent_app/core/widgets/foundation/containers/ia_rounded_container.dart';
import 'package:ivent_app/features/auth/constants/validation_constants.dart';
import 'package:ivent_app/shared/domain/entities/hobby.dart';

class HobbyCategoryBox extends StatefulWidget {
  final void Function(String hobbyId) onHobbyToggle;
  final List<String> selectedHobbyIds;
  final String mainCategory;
  final List<Hobby> hobbyList;

  const HobbyCategoryBox({
    super.key,
    required this.onHobbyToggle,
    required this.selectedHobbyIds,
    required this.mainCategory,
    required this.hobbyList,
  });

  @override
  State<HobbyCategoryBox> createState() => _HobbyCategoryBoxState();
}

class _HobbyCategoryBoxState extends State<HobbyCategoryBox> {
  bool _isExpanded = false;
  void Function(String hobbyId) get onHobbyToggle => widget.onHobbyToggle;
  List<String> get selectedHobbyIds => widget.selectedHobbyIds;
  String get mainCategory => widget.mainCategory;
  List<Hobby> get hobbyList => widget.hobbyList;
  void _toggleExpandedCategory() => setState(() => _isExpanded = !_isExpanded);

  @override
  Widget build(BuildContext context) {
    return IaRoundedContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCategoryTitle(),
          const SizedBox(height: AppDimensions.padding12),
          _buildHobbyTags(),
          if (_shouldShowExpandButton()) _buildExpandButton(),
        ],
      ),
    );
  }

  Widget _buildCategoryTitle() {
    return Text(
      mainCategory,
      style: AppTextStyles.size24Bold,
    );
  }

  Widget _buildHobbyTags() {
    const int maxInitialDisplay = AuthValidationConstants.maxInitialHobbiesDisplayed;
    final int displayCount = _isExpanded ? hobbyList.length : maxInitialDisplay;

    return Wrap(
      crossAxisAlignment: WrapCrossAlignment.start,
      spacing: AppDimensions.padding8,
      runSpacing: AppDimensions.padding8,
      children: hobbyList
          .take(displayCount)
          .map((hobby) => HobbyButtons.hobbyTag(
                onTap: () => onHobbyToggle(hobby.hobbyId),
                text: hobby.hobbyName,
                isSelected: selectedHobbyIds.contains(hobby.hobbyId),
              ))
          .toList(),
    );
  }

  Widget _buildExpandButton() {
    return HobbyButtons.expandCategory(
      margin: const EdgeInsets.only(top: AppDimensions.padding12),
      onPressed: _toggleExpandedCategory,
      isExpanded: _isExpanded,
    );
  }

  bool _shouldShowExpandButton() {
    return hobbyList.length > AuthValidationConstants.maxInitialHobbiesDisplayed;
  }
}
