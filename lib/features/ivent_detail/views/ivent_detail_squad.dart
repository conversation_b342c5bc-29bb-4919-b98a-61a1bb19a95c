import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/composite/buttons/shared_buttons.dart';
import 'package:ivent_app/core/widgets/composite/tiles/ia_list_tile.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_loading_indicator.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_details_controller.dart';
import 'package:ivent_app/features/profile/profile_pages.dart';

class IventDetailSquad extends StatefulWidget {
  final String iventId;

  const IventDetailSquad(this.iventId, {Key? key}) : super(key: key);

  @override
  State<IventDetailSquad> createState() => _IventDetailSquadState();
}

class _IventDetailSquadState extends State<IventDetailSquad> {
  late final IventDetailsController _controller;
  final TextEditingController _searchBarController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _controller = Get.find(tag: widget.iventId);
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final pageContent = _controller.participantsController.participants;
      if (pageContent == null) {
        return const IaLoadingIndicator();
      }
      return IaScaffold.search(
        title: 'iVent Ekibin',
        trailing: Text('${pageContent.userCount} Kişi', style: AppTextStyles.size16MediumTextSecondary),
        textEditingController: _searchBarController,
        body: ListView.separated(
          padding: const EdgeInsets.only(bottom: 100),
          itemCount: pageContent.userCount + 1,
          itemBuilder: (context, index) {
            if (index == 0) return _getInviteMoreTile();
            final element = pageContent.users[index - 1];
            return IaListTile.withImageUrl(
              onTap: () => Get.toNamed(ProfilePages.userProfile, parameters: {'id': element.userId}),
              avatarUrl: element.avatarUrl,
              title: '@${element.username}',
              subtitle: element.university,
              trailing: SharedButtons.addFriendListTile(relationshipStatus: element.relationshipStatus),
            );
          },
          separatorBuilder: IaListTile.separatorBuilder20,
        ),
      );
    });
  }

  IaListTile _getInviteMoreTile() {
    return IaListTile.withSvgIcon(
      iconPath: AppAssets.userAdd,
      title: 'Daha fazla kişi çağır',
      onTap: () => _controller.goToInviteMoreUsersPage(),
    );
  }
}
