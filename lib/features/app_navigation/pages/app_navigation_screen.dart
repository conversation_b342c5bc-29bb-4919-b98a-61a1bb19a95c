import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/features/app_navigation/controllers/app_navigation_controller.dart';
import 'package:ivent_app/features/app_navigation/models/app_navigation_data.dart';
import 'package:ivent_app/features/app_navigation/widgets/ia_navigation_scaffold.dart';
import 'package:ivent_app/features/home/<USER>/home_page.dart';
import 'package:ivent_app/features/notifications/pages/notifications_page.dart';
import 'package:ivent_app/features/profile/views/user_profile.dart';
import 'package:ivent_app/features/vibes/pages/camera_page.dart';
import 'package:ivent_app/features/vibes/pages/vibes_page.dart';

class AppNavigationScreen extends GetView<AppNavigationController> {
  const AppNavigationScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return IaNavigationScaffold(
      navigationDataList: [
        const AppNavigationData(iconPath: AppAssets.house01, screen: HomePage()),
        const AppNavigationData(iconPath: AppAssets.play, screen: VibesPage(), isNavBarVisible: false),
        const AppNavigationData(iconPath: AppAssets.camera, screen: CameraPage(), isNavBarVisible: false),
        const AppNavigationData(iconPath: AppAssets.bell, screen: NotificationsPage()),
        AppNavigationData(iconPath: AppAssets.user01, screen: UserProfile(userId: controller.sessionUser.sessionId)),
      ],
    );
  }
}
