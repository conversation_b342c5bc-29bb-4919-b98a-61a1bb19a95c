import 'package:flutter/widgets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/composite/buttons/navigation_buttons.dart';
import 'package:ivent_app/core/widgets/foundation/containers/ia_rounded_container.dart';
import 'package:ivent_app/features/app_navigation/models/app_navigation_data.dart';

class IaBottomNavigationBar extends StatefulWidget {
  final Function(int index) onIndexChanged;
  final int activeIndex;
  final double height;
  final List<AppNavigationData> navigationDataList;

  const IaBottomNavigationBar({
    super.key,
    required this.onIndexChanged,
    required this.activeIndex,
    required this.navigationDataList,
    this.height = AppDimensions.bottomNavigationBarHeight,
  });

  @override
  State<IaBottomNavigationBar> createState() => _IaBottomNavigationBarState();
}

class _IaBottomNavigationBarState extends State<IaBottomNavigationBar> {
  @override
  Widget build(BuildContext context) {
    return IaRoundedContainer(
      height: widget.height,
      color: AppColors.white,
      boxShadow: AppColors.usualShadow,
      child: Row(
        children: widget.navigationDataList.asMap().entries.map((entry) {
          final index = entry.key;
          final data = entry.value;
          return Expanded(
            child: NavigationButtons.navigationBar(
              onTap: () => widget.onIndexChanged(index),
              iconPath: data.iconPath,
              isActive: widget.activeIndex == index,
            ),
          );
        }).toList(),
      ),
    );
  }
}
