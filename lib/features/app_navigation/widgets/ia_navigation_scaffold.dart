import 'package:flutter/material.dart';
import 'package:ivent_app/features/app_navigation/models/app_navigation_data.dart';
import 'package:ivent_app/features/app_navigation/widgets/ia_bottom_navigation_bar.dart';
import 'package:ivent_app/features/side_menu/widgets/ia_profile_simple_drawer.dart';

class IaNavigationScaffold extends StatefulWidget {
  final List<AppNavigationData> navigationDataList;

  const IaNavigationScaffold({
    super.key,
    required this.navigationDataList,
  });

  @override
  State<IaNavigationScaffold> createState() => _IaNavigationScaffoldState();
}

class _IaNavigationScaffoldState extends State<IaNavigationScaffold> {
  int _view = 0;
  // Keep track of navigation history
  final List<int> _navigationHistory = [0];

  void _goBack() {
    _navigationHistory.removeLast();
    setState(() => _view = _navigationHistory.last);
  }

  void _goToTab(int index) {
    if (index != _view) {
      setState(() {
        _view = index;
        _navigationHistory.add(index);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: _navigationHistory.length <= 1,
      onPopInvokedWithResult: (didPop, result) {
        if (didPop) return;
        // If we have navigation history, go back to previous view
        if (_navigationHistory.length > 1) _goBack();
      },
      child: Scaffold(
        drawer: _view == 4 ? const IaProfileSimpleDrawer() : null, // Sadece profil sekmesinde drawer göster
        body: widget.navigationDataList[_view].screen,
        bottomNavigationBar: widget.navigationDataList[_view].isNavBarVisible
            ? IaBottomNavigationBar(
                onIndexChanged: _goToTab, activeIndex: _view, navigationDataList: widget.navigationDataList)
            : null,
      ),
    );
  }
}
