import 'package:ivent_app/features/mapbox/models/marker_feature.dart';

/// Removes duplicate map marker features based on their IDs
///
/// Takes a list of MarkerFeature items and returns a new list
/// with duplicate items (based on id) removed. Optionally takes
/// a list of items to exclude from the result.
List<MarkerFeature> removeDuplicateCoordinates(
  List<MarkerFeature> items, {
  List<MarkerFeature> exclude = const [],
}) {
  final Set<String> seenIds = exclude.map((val) => '${val.id}').toSet();
  final List<MarkerFeature> result = [];

  for (final item in items) {
    if (seenIds.contains(item.id)) continue;
    seenIds.add(item.id);
    result.add(item);
  }

  return result;
}
