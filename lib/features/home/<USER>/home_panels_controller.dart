import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/home/<USER>/home_state_manager.dart';
import 'package:ivent_app/features/home/<USER>';
import 'package:ivent_app/core/controllers/base_controller.dart';
import 'package:sliding_up_panel/sliding_up_panel.dart';

class HomePanelsController extends BaseController<HomeSharedState> {
  final PanelController panelController = PanelController();

  final _isPanelDraggable = true.obs;
  final _isPanelVisible = true.obs;
  final _screenIndex = 0.obs;

  bool get isPanelDraggable => _isPanelDraggable.value;
  bool get isPanelVisible => _isPanelVisible.value;
  int get screenIndex => _screenIndex.value;

  HomePanelsController(AuthService authService, HomeSharedState state) : super(authService, state);

  void goToFeedPage() {
    _screenIndex.value = 0;
    _isPanelDraggable.value = true;
    panelController.open();
  }

  void goToSearchPage() {
    _screenIndex.value = 1;
    _isPanelDraggable.value = false;
    panelController.open();
  }

  void goToFilterPage() {
    _screenIndex.value = 2;
    _isPanelDraggable.value = false;
    panelController.open();
  }

  void goToLocationPage() => Get.toNamed(HomePages.locationPage);

  void setPanelOpen() => _isPanelVisible.value = true;

  void setPanelClosed() => _isPanelVisible.value = false;
}
