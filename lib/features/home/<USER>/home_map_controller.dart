import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/home/<USER>/home_state_manager.dart';
import 'package:ivent_app/features/home/<USER>/remove_duplicate_coordinates.dart';
import 'package:ivent_app/features/mapbox/controllers/mapbox_controller.dart';
import 'package:ivent_app/features/mapbox/controllers/marker_controller.dart';
import 'package:ivent_app/features/mapbox/models/marker_feature.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';

class HomeMapController extends BaseController<HomeSharedState> {
  late final MapboxController mapboxController;

  final _iventBanners = <IventCardItem>[].obs;
  final _mapReturn = Rxn<MapReturn>();

  HomeMapController(AuthService authService, HomeSharedState state) : super(authService, state) {
    mapboxController = MapboxController(
      authService: authService,
      onSelectedFeaturesChanged: onSelectedFeaturesChanged,
    );
  }

  List<IventCardItem> get iventBanners => _iventBanners.toList();
  MapReturn? get mapReturn => _mapReturn.value;

  MarkerController get markerController => mapboxController.markerController;
  String get todayAsString => DateFormat('d MMMM EEEE').format(DateTime.now());

  @override
  Future<void> onInitAsync() async {
    super.onInitAsync();

    await mapboxController.getUserLocationCoordinates();
    await loadMapMarkers();
  }

  void setMapboxMap(MapboxMap map) => mapboxController.setMapboxMap(map);

  Future<void> updateVisibleMapBounds() async {
    await mapboxController.updateVisibleMapBounds();
    await loadMapMarkers();
  }

  Future<void> loadMapMarkers() async {
    final bounds = mapboxController.currentMapBounds;
    if (bounds == null) return;

    await runSafe(tag: 'loadMapMarkers', () async {
      _mapReturn.value = await homeApi.map(
        DateTime.parse('2018-01-01T00:00:00Z').toIso8601String(),
        DateTime.now().add(const Duration(days: 365 * 5)).toIso8601String(),
        bounds.latStart,
        bounds.latEnd,
        bounds.lngStart,
        bounds.lngEnd,
      );

      final result = _mapReturn.value!.ivents;
      List<MarkerFeature> markersToBeAdded = result.map((ivent) {
        return MarkerFeature(
          id: ivent.iventId,
          properties: MarkerFeatureProperties(isSelected: false),
          geometry: MarkerFeatureGeometry(
            coordinates: [ivent.longitude, ivent.latitude],
          ),
        );
      }).toList();
      debugPrint('Markers to be added: ${markersToBeAdded.map((e) => e.id).toList()}');

      markersToBeAdded = removeDuplicateCoordinates(
        markersToBeAdded,
        exclude: markerController.allFeatures,
      );
      markerController.addMarkers(markersToBeAdded);
    });
  }

  Future<void> onSelectedFeaturesChanged(List<MarkerFeature> selectedFeatures) async {
    if (selectedFeatures.isEmpty) {
      _iventBanners.value = [];
      return;
    }

    await runSafe(tag: 'getBannerByIventId', () async {
      final iventIds = selectedFeatures.map((feature) => feature.id).toList();
      final getBannerByIventIdResult = await iventsApi.getBannerByIventId(
        GetBannerByIventIdDto(iventIds: iventIds),
      );

      if (getBannerByIventIdResult != null) {
        _iventBanners.value = getBannerByIventIdResult.ivents;
      }
    });
  }

  Future<void> centerOnUserLocation() async => await mapboxController.moveCameraToUserLocation();

  bool isIventFavorited(String iventId) {
    final isGloballyFavorited = globalState.getIventFavoriteUpdate(iventId);
    final isLocallyFavorited = iventBanners.any((e) => e.iventId == iventId && e.isFavorited);
    return isGloballyFavorited ?? isLocallyFavorited;
  }

  void toggleFavorite(String iventId) async {
    await runSafe(tag: 'toggleFavorite', () async {
      if (isIventFavorited(iventId)) {
        _unfavoriteIvent(iventId);
      } else {
        _favoriteIvent(iventId);
      }
    });
  }

  void _favoriteIvent(String iventId) async {
    await iventsApi.favoriteIventByIventId(iventId);
    final index = iventBanners.indexWhere((item) => item.iventId == iventId);
    if (index != -1) {
      _iventBanners[index].isFavorited = true;
      _iventBanners[index] = _iventBanners[index];
    }
    globalState.updateIventFavorite(iventId, true);
  }

  void _unfavoriteIvent(String iventId) async {
    await iventsApi.unfavoriteIventByIventId(iventId);
    final index = iventBanners.indexWhere((item) => item.iventId == iventId);
    if (index != -1) {
      _iventBanners[index].isFavorited = false;
      _iventBanners[index] = _iventBanners[index];
    }
    globalState.updateIventFavorite(iventId, false);
  }
}
